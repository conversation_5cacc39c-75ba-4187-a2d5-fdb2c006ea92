'use client';

import React, { useEffect, useState, FC } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from '@headlessui/react';
import Tippy from '@tippyjs/react';
import { hideAll } from 'tippy.js';
import 'tippy.js/dist/tippy.css';
import { PiClockCounterClockwiseLight } from 'react-icons/pi';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  addTablePassengersDetails,
  deletePassengers,
  disableDriverQuery,
  getTablePassengerDetails,
} from '../state/queries';
import { ChevronDownIcon, DownloadIcon } from '@/icons';
import { queryClient } from '@/hooks/useGlobalContext';
import { toast, ToastContainer } from 'react-toastify';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { IoMdClose } from 'react-icons/io';
import { IoIosSearch } from 'react-icons/io';
import { FaPlus } from 'react-icons/fa6';
import { subYears } from 'date-fns';
import Input from '@/components/form/input/InputField';
import Select from '@/components/form/Select';
import { PassengersDetailsTypeResponse } from '@/utils/types';
import DeleteModal from '@/components/ui/modal/deletemodal';

const INITIAL_FORM_DATA = {
  fullName: '',
  emailId: '',
  contactNumber: '',
  gender: '',
  dateOfBirth: null as Date | null,
  currentAddress: '',
  preferredPaymentMethod: '',
  emergencyContactName: '',
  emergencyContactNumber: '',
  password: '',
};

const PAYMENT_OPTIONS = [
  { value: 'cash', label: 'Cash' },
  { value: 'card', label: 'Card' },
];
const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
];

const ActionsMenu: FC<{
  passenger: PassengersDetailsTypeResponse;
  onDisable: Function;
  onDelete: Function;
}> = ({ passenger, onDisable, onDelete }) => {
  const router = useRouter();
  return (
    <div className="flex flex-col space-y-1 p-1">
      <button
        className="w-full rounded px-2 py-1 text-left text-sm text-[#76787A] hover:bg-gray-100"
        onClick={() =>
          router.push(`/admin/passengers/${passenger.id}?edit=true`)
        }
      >
        Edit Profile
      </button>

      <button
        className="w-full rounded px-2 py-1 text-left text-sm text-[#76787A] hover:bg-gray-100"
        onClick={() => onDisable(passenger.id, 'suspended')}
      >
        Suspend Profile
      </button>
      <button
        className="w-full rounded px-2 py-1 text-left text-sm text-[#76787A] hover:bg-gray-100"
        onClick={() => onDisable(passenger.id, 'active')}
      >
        Reinstate Passenger
      </button>
      <button
        className="w-full rounded px-2 py-1 text-left text-sm text-[#76787A] hover:bg-gray-100"
        onClick={() => onDisable(passenger.id, 'inactive')}
      >
        Inactive Passenger
      </button>

      <button
        className="w-full rounded px-2 py-1 text-left text-sm text-[#76787A] hover:bg-gray-100"
        onClick={() => {
          toast.success('Notification sent!', {
            autoClose: 5000,
            position: 'top-center',
          });
          hideAll();
        }}
      >
        Send Notification
      </button>
      <button className="w-full rounded px-2 py-1 text-left text-sm text-[#76787A] hover:bg-gray-100">
        Download Report
      </button>

      <button className="w-full rounded px-2 py-1 text-left text-sm text-[#76787A] hover:bg-gray-100">
        Update Payment Method
      </button>

      <button className="w-full rounded px-2 py-1 text-left text-sm text-[#76787A] hover:bg-gray-100">
        Track Current ride
      </button>
      <button
        className="w-full rounded px-2 py-1 text-left text-sm text-red-600 hover:bg-red-50"
        onClick={() => onDelete(passenger.id)}
      >
        Delete Profile
      </button>
    </div>
  );
};

export default function PassengersTable() {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [clientId, setClientId] = useState('');
  const [formData, setFormData] = useState(INITIAL_FORM_DATA);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [deleteProfile, setDeleteProfile] = useState(false);

  const { push } = useRouter();

  const handleDeleteClick = (id: string) => {
    setDeleteProfile(true);
    setSelectedId(id);
    hideAll();
  };

  const { data: passengerData, isLoading } = useQuery({
    queryKey: ['passengerData', clientId],
    queryFn: () => getTablePassengerDetails(),
    enabled: true, // Only fetch if clientId is available
  });

  useEffect(() => {
    const account = localStorage.getItem('account');
    if (account) {
      try {
        const parsedAccount = JSON.parse(account);
        const id = parsedAccount?.client?.id;
        if (id) {
          setClientId(id);
        }
      } catch (e) {
        console.error('Failed to parse account from localStorage', e);
      }
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (date: Date | null) => {
    setFormData(prev => ({ ...prev, dateOfBirth: date }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validate = () => {
    const newErrors: Record<string, string> = {};
    if (!formData.fullName.trim())
      newErrors.fullName = 'Full name is required.';
    if (!formData.emailId.trim()) newErrors.emailId = 'Email is required.';
    else if (!/\S+@\S+\.\S+/.test(formData.emailId))
      newErrors.emailId = 'Email is invalid.';
    if (!formData.contactNumber.trim())
      newErrors.contactNumber = 'Contact number is required.';
    if (!formData.password || formData.password.length < 6)
      newErrors.password = 'Password must be at least 6 characters.';
    if (!formData.dateOfBirth)
      newErrors.dateOfBirth = 'Date of birth is required.';
    if (!formData.gender) newErrors.gender = 'Gender is required.';
    if (!formData.currentAddress.trim())
      newErrors.currentAddress = 'Address is required.';
    if (!formData.preferredPaymentMethod)
      newErrors.preferredPaymentMethod = 'Payment method is required.';
    if (!formData.emergencyContactName.trim())
      newErrors.emergencyContactName = 'Emergency contact name is required.';
    if (!formData.emergencyContactNumber.trim())
      newErrors.emergencyContactNumber =
        'Emergency contact number is required.';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const addPassengerMutation = useMutation({
    mutationFn: addTablePassengersDetails,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['passengerData', clientId] });
      toast.success('Passenger Added Successfully');
      setOpen(false);
      setFormData(INITIAL_FORM_DATA);
      setErrors({});
    },
    onError: () => toast.error('Failed to add passenger.'),
  });

  const handleFormSubmit = () => {
    if (validate()) {
      const payload = { ...formData, clientId };
      addPassengerMutation.mutate(payload);
    }
  };

  const deletePassengerMutation = useMutation({
    mutationFn: deletePassengers,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['passengerData', clientId] });
      toast.success('Passenger Deleted Successfully');
    },
    onError: () => toast.error('Failed to delete passenger.'),
  });

  const disablePassengerMutation = useMutation({
    mutationFn: disableDriverQuery,
    onSuccess: () => {
      toast.success('Passenger status updated.', {
        autoClose: 5000,
        position: 'top-center',
      });
      queryClient.invalidateQueries({ queryKey: ['passengerData', clientId] });

      hideAll();
    },
    onError: () => toast.error('Failed to update passenger status.'),
  });

  const getStatusClass = (status: string) => {
    const statusClasses = {
      active: 'text-[#13BB76]',
      inactive: 'text-[#8F8CD6]',
      suspended: 'text-[#FF4032]',
      enroute: 'text-[#1E90FF]',
    };
    return (
      statusClasses[status as keyof typeof statusClasses] || 'text-[#FF8C00]'
    );
  };
  function handleDisable(id: string, status: string) {
    disablePassengerMutation.mutate({ id, status });
  }

  return (
    <>
      <ToastContainer />
      <div className="tabled">
        <div className="flex justify-end">
          <button
            onClick={() => setOpen(true)}
            type="button"
            className="bg-cstm-blue-700 me-2 mb-4 flex items-center gap-2 rounded-full px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:outline-none"
          >
            <FaPlus /> Add New Passenger
          </button>
        </div>
        <div className="overflow-visible rounded-xl border border-gray-200 bg-white">
          <div className="header-bar bg-table-head flex items-center justify-between rounded-t-[12px] px-3 py-1">
            <form className="max-w-md flex-1">
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3">
                  <IoIosSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="search"
                  className="block w-full rounded-full border-transparent bg-white p-2 ps-10 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 sm:w-80"
                  placeholder="Search passengers..."
                  onChange={e => setSearchTerm(e.target.value)}
                />
              </div>
            </form>
            <div className="flex items-center gap-2">
              <button
                type="button"
                className="flex items-center justify-center rounded-full border bg-white p-2 text-gray-500 hover:bg-gray-100"
              >
                <PiClockCounterClockwiseLight size={20} />
              </button>
              <button
                type="button"
                className="flex items-center justify-center rounded-full border bg-white p-2 text-gray-500 hover:bg-gray-100"
              >
                <DownloadIcon />
              </button>
            </div>
          </div>
          <div className="custom-scrollbar max-w-full overflow-x-auto">
            <Table>
              <TableHeader className="bg-white">
                <TableRow className="border-b border-gray-200">
                  <TableCell
                    isHeader
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Name
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Contact No
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Status
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Address
                  </TableCell>
                  <TableCell
                    isHeader
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y bg-white">
                {isLoading ? (
                  <TableRow>
                    <TableCell
                      colSpan={5}
                      className="py-8 text-center text-gray-500"
                    >
                      Loading Passengers...
                    </TableCell>
                  </TableRow>
                ) : passengerData.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={5}
                      className="py-8 text-center text-gray-500"
                    >
                      No Passengers found
                    </TableCell>
                  </TableRow>
                ) : (
                  passengerData.map(order => (
                    <React.Fragment key={order.id}>
                      <TableRow className="cursor-pointer dark:hover:bg-gray-800">
                        <TableCell className="px-4 py-3 text-sm font-medium capitalize text-[#050013]">
                          <p
                            onClick={() =>
                              push(`/admin/passengers/${order.id}`)
                            }
                          >
                            {order.fullName}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3 text-sm text-[#050013]">
                          {order.contactNumber}
                        </TableCell>
                        <TableCell className="px-4 py-3">
                          <p
                            className={`text-sm capitalize ${getStatusClass(
                              order.status
                            )}`}
                          >
                            {order.status}
                          </p>
                        </TableCell>
                        <TableCell className="px-4 py-3 text-sm uppercase text-[#050013]">
                          {order.currentAddress}
                        </TableCell>
                        <TableCell className="px-4 py-3 text-center">
                          <Tippy
                            trigger="click"
                            interactive
                            content={
                              <ActionsMenu
                                passenger={order}
                                onDisable={handleDisable}
                                onDelete={handleDeleteClick}
                              />
                            }
                            placement="left-start"
                            theme="light"
                            arrow={false}
                            className="rounded-lg border border-gray-200 !bg-white !text-gray-900 shadow-sm"
                          >
                            <button
                              type="button"
                              className="text-[#76787A] focus:outline-none"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                                className="size-6"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M10.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </button>
                          </Tippy>
                        </TableCell>
                      </TableRow>
                    </React.Fragment>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        className="relative z-[1000]"
      >
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-[#2a2a2a50] bg-opacity-60 transition-opacity duration-500 ease-in-out data-[closed]:opacity-0"
        />
        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <DialogPanel
                transition
                className="pointer-events-auto relative w-screen max-w-xl transform rounded-tl-[50px] transition duration-500 ease-in-out data-[closed]:translate-x-full"
              >
                <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                  <div className="bg-[#F6F8FB] p-6">
                    <DialogTitle className="flex items-center justify-between text-xl font-semibold text-gray-900">
                      Passengers Details
                      <IoMdClose
                        className="cursor-pointer text-[#76787A] hover:text-[#3324E3]"
                        size={20}
                        onClick={() => {
                          setOpen(false);
                          setFormData(INITIAL_FORM_DATA);
                          setErrors({});
                        }}
                      />
                    </DialogTitle>
                  </div>
                  <div className="custom-scrollbar relative flex-1 overflow-y-auto px-6 py-4">
                    <div className="space-y-4">
                      <p className="text-sm font-medium text-gray-800">
                        Profile Overview
                      </p>
                      <Input
                        placeholder="Full Name*"
                        name="fullName"
                        onChange={handleChange}
                        error={errors.fullName}
                      />
                      <Input
                        placeholder="Email ID*"
                        name="emailId"
                        onChange={handleChange}
                        error={errors.emailId}
                      />
                      <Input
                        placeholder="Contact Number*"
                        name="contactNumber"
                        onChange={handleChange}
                        error={errors.contactNumber}
                      />
                      <Input
                        type="password"
                        placeholder="Password*"
                        name="password"
                        onChange={handleChange}
                        error={errors.password}
                      />
                      <div className="flex w-full gap-4">
                        <div className="relative w-full">
                          <Select
                            options={GENDER_OPTIONS}
                            placeholder="Gender*"
                            onChange={value =>
                              handleSelectChange('gender', value)
                            }
                          />
                          {errors.gender && (
                            <p className="mt-1 text-xs text-red-500">
                              {errors.gender}
                            </p>
                          )}
                        </div>
                        <div className="relative w-full">
                          <DatePicker
                            selected={formData.dateOfBirth}
                            className="w-full rounded-lg border border-gray-300 p-2 text-sm"
                            onChange={handleDateChange}
                            placeholderText="Date of Birth*"
                            maxDate={subYears(new Date(), 18)}
                            showYearDropdown
                            scrollableYearDropdown
                          />
                          {errors.dateOfBirth && (
                            <p className="mt-1 text-xs text-red-500">
                              {errors.dateOfBirth}
                            </p>
                          )}
                        </div>
                      </div>
                      <p className="pt-2 text-sm font-medium text-gray-800">
                        Address Details
                      </p>
                      <Input
                        placeholder="Current Address"
                        name="currentAddress"
                        onChange={handleChange}
                      />
                      <p className="pt-2 text-sm font-medium text-gray-800">
                        Payment Details
                      </p>
                      <Select
                        options={PAYMENT_OPTIONS}
                        placeholder="Preferred Payment Method"
                        onChange={value =>
                          handleSelectChange('preferredPaymentMethod', value)
                        }
                      />
                      <p className="pt-2 text-sm font-medium text-gray-800">
                        Emergency Contact
                      </p>
                      <Input
                        placeholder="Emergency Contact Name"
                        name="emergencyContactName"
                        onChange={handleChange}
                      />
                      <Input
                        placeholder="Emergency Contact Number"
                        name="emergencyContactNumber"
                        onChange={handleChange}
                      />
                    </div>
                  </div>
                  <div className="border-t px-6 py-4">
                    <button
                      onClick={handleFormSubmit}
                      type="button"
                      disabled={addPassengerMutation.isPending}
                      className="w-full rounded-full bg-blue-700 px-6 py-3 text-center text-sm font-medium text-white hover:bg-blue-800 disabled:opacity-60"
                    >
                      {addPassengerMutation.isPending
                        ? 'Processing...'
                        : 'Add New Passenger'}
                    </button>
                  </div>
                </div>
              </DialogPanel>
            </div>
          </div>
        </div>
      </Dialog>

      {selectedId && (
        <DeleteModal
          id={selectedId}
          isOpen={deleteProfile}
          setIsOpen={setDeleteProfile}
          handleDeleteDriver={() => {
            handleDeleteClick(selectedId);
            setSelectedId(null);
            deletePassengerMutation.mutate(selectedId);
          }}
          title="Delete Passenger"
          message="Are you sure you want to delete this passenger? This cannot be undone."
        />
      )}
    </>
  );
}
