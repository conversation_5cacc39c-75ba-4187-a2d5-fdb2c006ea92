import ky, { HTTPError } from 'ky';
import _ from 'lodash';
import { backendEndpoints, nextEndpoints } from '@/config/endpoints';
import { ERROR_MESSAGES } from '@/config/textConstants';
import { queryClient } from '@/hooks/useGlobalContext';

const handleForbidden = async (response: Response) => {
  const isSigninEndpoint = _.endsWith(
    response.url,
    backendEndpoints.AUTH.SIGNIN
  );
  if (isSigninEndpoint) return response;

  try {
    await backendApiClient.delete(nextEndpoints.AUTH.SIGNOUT);
  } catch (err) {
    console.error('Failed to sign out on 403:', err);
  }

  queryClient.clear();
  setTimeout(() => {
    window.location.replace('/signin');
  }, 1500);

  return response;
};

// Helper: Global error formatting
const parseError = async (defaultError: HTTPError) => {
  const error: HTTPError & { parsedResponse?: unknown } = { ...defaultError };
  const { response } = error;

  try {
    const data: any = await response.json();
    error.name = response.status.toString();
    error.message = data?.error || ERROR_MESSAGES.UNEXPECTED;
    error.parsedResponse = data;
  } catch {
    error.name = response.status.toString();
    error.message =
      response.status === 403
        ? ERROR_MESSAGES.SESSION_EXPIRED
        : ERROR_MESSAGES.UNEXPECTED;
  }

  return error;
};

// API Client with base config and hooks
export const backendApiClient = ky.extend({
  prefixUrl: process.env.NEXT_PUBLIC_PROXY_API_BASE_URL,
  retry: 0,
  timeout: 60000,
  hooks: {
    beforeRequest: [
      request => {
        const token = localStorage.getItem('token');
        if (token) {
          request.headers.set('Authorization', `Bearer ${token}`);
        }
      },
    ],
    afterResponse: [
      async (_input, _options, response) => {
        if (response.status === 403) {
          return handleForbidden(response);
        }
        if (response.status === 401) {
          localStorage.clear();
          queryClient.clear();
          window.location.replace('/login');
        }
        return response;
      },
    ],
    beforeError: [parseError],
  },
});

export const sendMultipart = async (
  url: string,
  payload: Record<string, any>,
  method: 'POST' | 'PATCH' | 'PUT' = 'POST'
) => {
  const formData = new FormData();

  Object.entries(payload).forEach(([key, value]) => {
    if (value instanceof File || value instanceof Blob) {
      formData.append(key, value);
    } else {
      formData.append(key, String(value));
    }
  });

  if (method === 'PATCH') {
    return backendApiClient.patch(url, {
      body: formData,
    });
  }
  if (method === 'PUT') {
    return backendApiClient.put(url, {
      body: formData,
    });
  }

  return backendApiClient.post(url, {
    body: formData,
  });
};

export const externalApiClient = ky.extend({});
